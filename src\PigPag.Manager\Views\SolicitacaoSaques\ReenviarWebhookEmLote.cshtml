
<section class="content-header" style="margin-bottom: 20px">
    <h1>
        Reenviar confirmação de saque processado
    </h1>
</section>
<section class="content">
    <div class="form-group">
        <label>Date and time range:</label>

        <div class="input-group">
            <div class="input-group-addon">
                <i class="fa fa-clock-o"></i>
            </div>
            <input type="text" class="form-control pull-right" id="reservationtime">
        </div>
        <!-- /.input group -->
        <div class="progress-group">
            <span class="progress-text">Aguardando</span>
            <div class="progress">
                <div class="progress-bar progress-bar-primary progress-bar-striped" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%">
                    <span class="">0% Complete (success)</span>
                </div>
            </div>
        </div>
    </div>
    <div class="div-alert-procesamento callout callout-warning hide">
        <h4><i class="icon fa fa-warning"></i> Atenção!</h4>
        <p>Processamento em andamento. Favor não fechar esta janela.</p>
    </div>
    <div class="div-sucesso-procesamento callout callout-success hide">
        <h4>Sucesso!</h4>
        <p>Processamento finalizado com sucesso.</p>
    </div>
    <style>
        div.progress > div span {
            color: black;
        }
    </style>
</section>
<script>
    $(function () {
        $('#reservationtime').daterangepicker({
            timePicker: true, timePickerIncrement: 5, locale: { format: 'MM/DD/YYYY h:mm A' }
        }, function (start, end) {

            $('#reservationtime span').html(start.format('MMMM D, YYYY h:mm A') + ' - ' + end.format('MMMM D, YYYY'));
            obterSolicitacaoSaque(start, end);
        });

        initDaterangepicker();

    });
    function increment(progress, porcentagem) {

        porcentagem = ((porcentagem > 100 ? 100 : porcentagem) * 100).toFixed(2);

        progress.attr('aria-valuenow', porcentagem);
        progress.css('width', `${porcentagem}%`);
        $('>span', progress).html(`${porcentagem}% Completo`);
    }
    async function obterSolicitacaoSaque(start, end) {
        $("#dialogProcessando").modal('show');
        var saques = await $.getJSON('/SolicitacaoSaques/ObterSaquesParaReenviarWebhookEmLote?di=' + encodeURIComponent(start.toJSON()) + '&df=' + encodeURIComponent(end.toJSON()));

        $("#dialogProcessando").modal('hide');

        if (!Array.isArray(saques)) {
            return;
        }
        $('#reservationtime').prop('disabled', true);

        $('.div-alert-procesamento').removeClass('hide');
        $('.div-sucesso-procesamento').addClass('hide');

        var total = saques.length;
        const progress = $('div.progress > div');

        for (var i = 0; i < total; i++) {

            try {
                var saque = saques[i];

                if (!saque.Id)
                    continue;

                var codigo = saque.Codigo;
                console.log(`Processando ${codigo}`);
                $('.progress-text').html(`Processando ${i + 1} de ${total}`);
                var id = saque.Id;

                var result = await $.post('/SolicitacaoSaques/ReenviarSaqueFilaAsync', {
                    idSolicitacaoSaque: saque.Id
                });

                var porcentagem = (i + 1) / total;

                increment(progress, porcentagem);
            } catch (e) {
                console.error(e);
                continue;
            }
        }
        $('#reservationtime').prop('disabled', false);

        $('.div-alert-procesamento').addClass('hide');
        $('.div-sucesso-procesamento').removeClass('hide');

        $('.progress-text').html(`Aguardando`);
        increment(progress, 0);

    }
</script>
