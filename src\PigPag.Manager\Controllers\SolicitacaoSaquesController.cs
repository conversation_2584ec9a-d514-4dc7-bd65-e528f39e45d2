using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.DependencyInjection;
using Multipay.Service.Criptography;
using MultiPay.DomainService.Mongo.Services;
using MultiPay.PIX.Integracao;
using MultiPay.Plugin.RabbitMqBase.Bus;
using MultiPay.Plugin.RabbitMqBase.Events;
using MultiPay.Plugin.SharedServices;
using Newtonsoft.Json;
using PagedList;
using PigPag.Common;
using PigPag.Constants;
using PigPag.DBDomain;
using PigPag.Manager.Attributes;
using PigPag.Manager.Models;
using PigPag.Manager.Models.SolicitacaoSaque;
using PigPag.Plugin.BS2;
using PigPag.Resources;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Transactions;
using System.Web.Mvc;
using System.Web.Script.Serialization;
using static PigPag.Constants.Constant;

namespace PigPag.Manager.Controllers
{
    public class SolicitacaoSaquesController : BaseAutorizacaoController
    {
        public class ListaCodigos
        {
            public List<string> Codigos { get; set; }
        }

        #region Privados

        private async Task<string> EnviarWebhookSolicitacaoSaque(string codigoSolicitacaoSaque)
        {
            var usuarioLogado = new Domain.Manager.UsuarioAdministrativo().SelectUsuarioAdministrativoLogado();

            return await EnviarWebhookSolicitacaoSaque(await new Domain.Manager.SolicitacaoSaque().SelectSolicitacaoSaqueDadosBasicosPorCodigo(codigoSolicitacaoSaque), $"{usuarioLogado.Login} - {usuarioLogado.Nome}");
        }

        private Task<string> EnviarWebhookSolicitacaoSaque(DataModel.SolicitacaoSaque.SelectSolicitacaoSaqueDadosBasicosPorIdClienteReturnModel solicitacaoSaque, string userInfo)
        {
            try
            {
                var status = "";

                if (solicitacaoSaque.DataPagamento.HasValue && solicitacaoSaque.DataPagamento.HasValue && solicitacaoSaque.DataConclusao.HasValue && !solicitacaoSaque.DataEstorno.HasValue)
                    status = "Completed";
                else if (solicitacaoSaque.DataEstorno.HasValue)
                    status = "Chargeback";
                else if (solicitacaoSaque.DataPagamento.HasValue && !solicitacaoSaque.DataConclusao.HasValue)
                    status = "Paid and Waiting Confirmation";
                else
                    status = "Unknown";

                new SolicitacaoSaqueModel().AdicionarLog(solicitacaoSaque.Id, string.Format("[Enviado para mensageria] Reenvio de webhook solicitado por: {0}", userInfo));

                var bus = ServiceProvider.GetRequiredService<IBusReenviarSolicitacaoSaques>();

                var statusCode = "01";
                var sucesso = true;

                if (solicitacaoSaque.DataEstorno.HasValue)
                {
                    sucesso = false;
                    status = "RETURNED TRANSACTION";
                    statusCode = "02";
                }

                bus.Publicar(new ReenviarSolicitacaoSaqueEvent(sucesso, solicitacaoSaque.Id, status, statusCode));

                return Task.FromResult(JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.success, false, true, "Webhook reenviado para mensageria com sucesso!")));
            }
            catch (Exception ex)
            {
                return Task.FromResult(JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.error, true, true, $"Erro ao enviar webhook! Erro: {ex.Message}")));
            }
        }

        #endregion Privados

        private IServiceProvider ServiceProvider { get; }

        private readonly CriptoService _criptoService;

        public SolicitacaoSaquesController(IServiceProvider serviceProvider, CriptoService criptoService)
        {
            ServiceProvider = serviceProvider;
            _criptoService = criptoService;
        }

        public ActionResult Index(int? pg)
        {
            if (TempData["Mensagem"] != null)
                ViewBag.Mensagem = TempData["Mensagem"];
            return View();
        }

        public async Task<ActionResult> _PartialListarSolicitacaoSaque(int? pg, string codigo, string customId, string nomeFavorecido, string cpfCnpjFavorecido, string nomeCorrentista, string cpfCnpjCorrentista, string di, string df, string endToEnd, int idBanco, string ordemTransacao, int? idContaFavorecido = null, byte? status = null, int itensPorPagina = 25)
        {
            if (TempData["Mensagem"] != null)
                ViewBag.Mensagem = TempData["Mensagem"];
            pg = pg ?? 1;
            if (!string.IsNullOrEmpty(codigo))
                codigo = codigo.ToUpperInvariant();
            else
                codigo = null;
            if (!string.IsNullOrEmpty(customId))
                customId = customId.Trim();
            else
                customId = null;
            if (!string.IsNullOrEmpty(endToEnd))
                endToEnd = endToEnd.Trim();
            else
                endToEnd = null;
            if (!string.IsNullOrEmpty(nomeCorrentista))
                nomeCorrentista = nomeCorrentista.ToUpperInvariant();
            else
                nomeCorrentista = null;
            if (!string.IsNullOrEmpty(cpfCnpjCorrentista))
                cpfCnpjCorrentista = Common.StringFormat.RetornarApenasNumeros(cpfCnpjCorrentista);
            else
                cpfCnpjCorrentista = null;
            if (!string.IsNullOrEmpty(nomeFavorecido))
                nomeFavorecido = nomeFavorecido.ToUpperInvariant();
            else
                nomeFavorecido = null;
            if (!string.IsNullOrEmpty(cpfCnpjFavorecido))
                cpfCnpjFavorecido = Common.StringFormat.RetornarApenasNumeros(cpfCnpjFavorecido);
            else
                cpfCnpjFavorecido = null;
            DateTime? dataInicio, dataFim;
            if (!string.IsNullOrEmpty(di))
                dataInicio = Convert.ToDateTime(di);
            else
                dataInicio = (DateTime?)null;
            if (!string.IsNullOrEmpty(df))
                dataFim = Convert.ToDateTime(df);
            else
                dataFim = (DateTime?)null;

            if (status == null || status == 0)
                status = (byte?)null;
            if (!string.IsNullOrEmpty(endToEnd))
                endToEnd = endToEnd.Trim();
            else
                endToEnd = null;

            var solicitacaoSaque = await new Domain.Manager.SolicitacaoSaque().SelectSolicitacaoSaquePorPeriodo(dataInicio, dataFim, codigo, customId, endToEnd, nomeFavorecido, cpfCnpjFavorecido, nomeCorrentista, cpfCnpjCorrentista, idBanco, ordemTransacao, idContaFavorecido, status);

            return PartialView(new ListarSolicitacaoSaqueModel() { SolicitacoesSaque = solicitacaoSaque.OrderByDescending(x => x.DataSolicitacao).ToPagedList(pg.Value, itensPorPagina) });
        }

        [HttpPost]
        public async Task<string> AprovarSolicitacaoSaque(int idSolicitacao, string justificativa)
        {
            if (string.IsNullOrWhiteSpace(justificativa))
            {
                return new JavaScriptSerializer().Serialize(new
                {
                    type = "error",
                    error = true,
                    message = "Favor informar a justificativa para autorização do saque."
                });
            }

            var message = new SolicitacaoSaquesRecebida();

            var distributedCache = ServiceProvider.GetService<IDistributedCache>();

            var keyCached = $"AGUADANDO_APR_SAQUE:{idSolicitacao}";
            byte[] encodedCached = null;

            if (distributedCache != null)
                encodedCached = await distributedCache.GetAsync(keyCached);

            if (encodedCached == null)
            {
                var solicitacaoSaque = await db.SolicitacaoSaque
                    .AsNoTracking()
                    .Include(a => a.FavorecidoCliente)
                    .Include(a => a.ContaBancariaFavorecidoCliente)
                    .Include(a => a.ContaBancaria)
                    .Include(a => a.ContaBancaria.Banco)
                    .Include(a => a.ContaBancaria.TipoContaBancaria)
                    .Where(a => a.Id == idSolicitacao)
                    .FirstOrDefaultAsync();

                message = new SolicitacaoSaquesRecebida()
                {
                    CodigoOperacaoCliente = solicitacaoSaque.Codigo,
                    IdSolicitacaoSaque = solicitacaoSaque.Id,
                    Ticket = null,
                    IdCliente = solicitacaoSaque.IdCliente,
                    TipoNegocio = $"{BancoInfinity.TipoDeNegocio}",
                    Valor = solicitacaoSaque.ValorSolicitado,
                    ContaBancariaFavorecido = new SolicitacaoSaquesRecebida.ContaBancariaFavorecidoData
                    {
                        Documento = solicitacaoSaque.FavorecidoCliente.CPFCNPJ,
                        NomeRazao = solicitacaoSaque.FavorecidoCliente.Nome,
                        TipoChavePix = solicitacaoSaque.ContaBancariaFavorecidoCliente.IdTipoChavePIX.ToString(),
                        ValorChavePix = solicitacaoSaque.ContaBancariaFavorecidoCliente.ChavePIX
                    }
                };

                if (solicitacaoSaque.ContaBancaria != null)
                {
                    message.ContaBancariaFavorecido.DadosBancario = new SolicitacaoSaquesRecebida.ContaBancariaFavorecidoData.DadosBancarioData
                    {
                        Agencia = solicitacaoSaque.ContaBancaria.Agencia,
                        Conta = solicitacaoSaque.ContaBancaria.Conta,
                        Ispb = solicitacaoSaque.ContaBancaria.Banco.ISPB,
                        NomeBanco = solicitacaoSaque.ContaBancaria.Banco.Nome,
                        Tipo = solicitacaoSaque.ContaBancaria.TipoContaBancaria.Nome
                    };
                }
            }
            else
            {
                var messageEvent = Encoding.UTF8.GetString(encodedCached);

                message = JsonConvert.DeserializeObject<SolicitacaoSaquesRecebida>(messageEvent);
            }

            message.Aprovado = true;

            var usuarioAdmin = new UsuarioAdministrativoModel().SelectUsuarioAdministrativoLogado();
            new SolicitacaoSaqueModel().AdicionarLog(idSolicitacao, $"Saque autorizado por {usuarioAdmin.Nome}. Justificativa: {justificativa}");

            var bus = ServiceProvider.GetService<IBusSolicitacaoSaques>();

            if (bus == null)
            {
                return new JavaScriptSerializer().Serialize(new
                {
                    type = "error",
                    error = true,
                    message = "Configuração da mensageria inconsistente. Favor entrar em contato com suporte."
                });
            }

            bus.PublicarSolicitacaoSaquesRecebida(message);

            if (encodedCached != null)
                await distributedCache.RemoveAsync(keyCached);

            return new JavaScriptSerializer().Serialize(new
            {
                type = "success",
                error = false,
                message = "Saque autorizado com sucesso!"
            });
        }

        public async Task<ActionResult> ListarSolicitacaoSaque(int? pg, string codigo, string customId, string endToEnd, string nomeFavorecido, string cpfCnpjFavorecido, string nomeCorrentista, string cpfCnpjCorrentista, string di, string df, int? idBanco, string origemTransacao, int? idContaFavorecido = null, byte? status = null, int itensPorPagina = 25)
        {
            if (TempData["Mensagem"] != null)
                ViewBag.Mensagem = TempData["Mensagem"];
            pg = pg ?? 1;
            if (!string.IsNullOrEmpty(codigo))
                codigo = codigo.ToUpperInvariant();
            else
                codigo = null;
            if (!string.IsNullOrEmpty(customId))
                customId = customId.Trim();
            else
                customId = null;
            if (!string.IsNullOrEmpty(endToEnd))
                endToEnd = endToEnd.Trim();
            else
                endToEnd = null;
            if (!string.IsNullOrEmpty(nomeCorrentista))
                nomeCorrentista = nomeCorrentista.ToUpperInvariant();
            else
                nomeCorrentista = null;
            if (!string.IsNullOrEmpty(cpfCnpjCorrentista))
                cpfCnpjCorrentista = Common.StringFormat.RetornarApenasNumeros(cpfCnpjCorrentista);
            else
                cpfCnpjCorrentista = null;
            if (!string.IsNullOrEmpty(nomeFavorecido))
                nomeFavorecido = nomeFavorecido.ToUpperInvariant();
            else
                nomeFavorecido = null;
            if (!string.IsNullOrEmpty(cpfCnpjFavorecido))
                cpfCnpjFavorecido = Common.StringFormat.RetornarApenasNumeros(cpfCnpjFavorecido);
            else
                cpfCnpjFavorecido = null;

            if (!string.IsNullOrEmpty(origemTransacao))
                origemTransacao = origemTransacao.Trim();
            else
                origemTransacao = null;

            DateTime? dataInicio, dataFim;
            if (!string.IsNullOrEmpty(di))
                dataInicio = Convert.ToDateTime(di);
            else
                dataInicio = (DateTime?)null;
            if (!string.IsNullOrEmpty(df))
                dataFim = Convert.ToDateTime(df);
            else
                dataFim = (DateTime?)null;

            if (status == null || status == 0)
                status = (byte?)null;

            var dataBase = await new Domain.Manager.SolicitacaoSaque().SelectSolicitacaoSaquePorPeriodo(dataInicio, dataFim, codigo, customId, endToEnd, nomeFavorecido, cpfCnpjFavorecido, nomeCorrentista, cpfCnpjCorrentista, (int)idBanco, origemTransacao, idContaFavorecido, status, (int)pg, itensPorPagina);
            var json = Json(dataBase, JsonRequestBehavior.AllowGet);
            json.MaxJsonLength = Int32.MaxValue;
            return json;
        }

        [HttpPost]
        public async Task<string> EstornarSolicitacaoSaqueMassa(ListaCodigos lista)
        {
            try
            {
                var usuarioAdmin = new UsuarioAdministrativoModel().SelectUsuarioAdministrativoLogado();

                var saques = await db.SolicitacaoSaque
                    .Include("SaidaCliente")
                    .Where(x => lista.Codigos.Contains(x.Codigo))
                    .ToListAsync();

                foreach (var solicitacao in saques)
                {
                    if (solicitacao.DataPagamento.HasValue)
                    {
                        new SolicitacaoSaqueModel().AdicionarLog(solicitacao.Id, "Não é possível estornar um saque já concluído e marcado como \"Pago\".");
                        continue;
                    }

                    if (!solicitacao.DataEstorno.HasValue)
                        await new PigPag.Domain.Manager.SolicitacaoSaque().EstornarSaqueAsync(solicitacao.Id, "Estorno admin");

                    new SolicitacaoSaqueModel().AdicionarLog(solicitacao.Id, string.Format("Estorno feito por: {0} - {1}", usuarioAdmin.Login, usuarioAdmin.Nome));

                    if (!string.IsNullOrEmpty(solicitacao.UrlAtualizacao))
                        await EnviarWebhookSolicitacaoSaque(solicitacao.Codigo);
                }

                return $"Solicitaç{(lista.Codigos.Count > 1 ? "ões" : "ão")} de saque estornada{(lista.Codigos.Count > 1 ? "s" : "")} com sucesso!";
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public ActionResult ListaSaquesAguardandoConclusao()
        {
            return View();
        }

        public async Task<ActionResult> ListarSaquesAguardandoConclusao()
        {
            var chavePIXContaBancariaFavorecidoCliente = (from c in db.ChavePIXContaBancariaFavorecidoCliente
                                                          select new
                                                          {
                                                              Id = c.Id,
                                                              ChavePIX = c.ChavePIX,
                                                              ChavePIXValida = c.ChavePIXValida,
                                                              DataCadastro = c.DataCadastro,
                                                              DataProcessamento = c.DataProcessamento,
                                                              DataValidacao = c.DataValidacao,
                                                              OrigemValidacao = c.OrigemValidacao
                                                          });
            var json = Json(await chavePIXContaBancariaFavorecidoCliente.ToListAsync(), JsonRequestBehavior.AllowGet);
            json.MaxJsonLength = Int32.MaxValue;
            return json;
        }

        public async Task<ActionResult> Details(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            var usuarioLogado = new PigPag.Domain.Manager.UsuarioAdministrativo().SelectUsuarioAdministrativoLogado();
            PigPag.DBDomain.SolicitacaoSaque solicitacaoSaque = await db.SolicitacaoSaque
                .Where(item => usuarioLogado.IdOperador == null || usuarioLogado.IdOperador == item.Cliente.IdOperador)
                .FirstOrDefaultAsync(item => item.Id == id);

            if (solicitacaoSaque == null)
            {
                return HttpNotFound();
            }
            return View(solicitacaoSaque);
        }

        [ExigePermissaoEscrita]
        public ActionResult Create()
        {
            ViewBag.IdCliente = new SelectList(ClientesBaas(), "Id", "Codigo");
            ViewBag.IdContaBancaria = new SelectList(ContaBancariaBaas(), "Id", "Agencia");
            ViewBag.IdContaBancariaEmpresaPagou = new SelectList(db.ContaBancariaEmpresa, "Id", "Agencia");
            ViewBag.IdOrdemPagamento = new SelectList(OrdemPagamentoBaas(), "Id", "Codigo");
            ViewBag.IdSaidaCliente = new SelectList(SaidaClienteBaas(), "Id", "Descricao");
            ViewBag.IdStatusSolicitacaoSaque = new SelectList(db.StatusSolicitacaoSaque, "Id", "Nome");
            ViewBag.IdTipoOperacao = new SelectList(db.TipoOperacaoBancaria, "Id", "Nome");
            ViewBag.IdUsuarioEstorno = new SelectList(UsuarioAdministrativoBaas(), "Id", "Nome");
            return View();
        }

        [HttpPost]
        [ExigePermissaoEscrita]
        public async Task<ActionResult> Create([Bind(Include = "Id,IdCliente,IdSaidaCliente,IdStatusSolicitacaoSaque,IdContaBancaria,IdOrdemPagamento,IdTipoOperacao,IdUsuarioEstorno,IdContaBancariaEmpresaPagou,Codigo,ValorSolicitado,ValorTaxa,ValorTarifa,ValorLiquido,DataSolicitacao,JustificativaReprovacao,DataEstorno,JustificativaEstorno,DataProcessamento,DataPagamento,DataConclusao,Observacao")] PigPag.DBDomain.SolicitacaoSaque solicitacaoSaque)
        {
            if (ModelState.IsValid)
            {
                db.SolicitacaoSaque.Add(solicitacaoSaque);
                await db.SaveChangesAsync();
                return RedirectToAction("Index");
            }

            ViewBag.IdCliente = new SelectList(ClientesBaas(), "Id", "Codigo", solicitacaoSaque.IdCliente);
            ViewBag.IdContaBancaria = new SelectList(ContaBancariaBaas(), "Id", "Agencia", solicitacaoSaque.IdContaBancaria);
            ViewBag.IdContaBancariaEmpresaPagou = new SelectList(db.ContaBancariaEmpresa, "Id", "Agencia", solicitacaoSaque.IdContaBancariaEmpresaPagou);
            ViewBag.IdOrdemPagamento = new SelectList(OrdemPagamentoBaas(), "Id", "Codigo", solicitacaoSaque.IdOrdemPagamento);
            ViewBag.IdSaidaCliente = new SelectList(SaidaClienteBaas(), "Id", "Descricao", solicitacaoSaque.IdSaidaCliente);
            ViewBag.IdStatusSolicitacaoSaque = new SelectList(db.StatusSolicitacaoSaque, "Id", "Nome", solicitacaoSaque.IdStatusSolicitacaoSaque);
            ViewBag.IdTipoOperacao = new SelectList(db.TipoOperacaoBancaria, "Id", "Nome", solicitacaoSaque.IdTipoOperacao);
            ViewBag.IdUsuarioEstorno = new SelectList(UsuarioAdministrativoBaas(), "Id", "Nome", solicitacaoSaque.IdUsuarioEstorno);
            return View(solicitacaoSaque);
        }

        public async Task<ActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            PigPag.DBDomain.SolicitacaoSaque solicitacaoSaque = await db.SolicitacaoSaque.FindAsync(id);
            if (solicitacaoSaque == null)
            {
                return HttpNotFound();
            }
            ViewBag.IdCliente = new SelectList(ClientesBaas(), "Id", "Codigo", solicitacaoSaque.IdCliente);
            ViewBag.IdContaBancaria = new SelectList(ContaBancariaBaas(), "Id", "Agencia", solicitacaoSaque.IdContaBancaria);
            ViewBag.IdContaBancariaEmpresaPagou = db.ContaBancariaEmpresa.ToList().Select(x => new SelectListItem() { Value = x.Id.ToString(), Text = string.Format("{0} - {1} - {2}", x.Banco?.Nome, x.Agencia, x.Conta) });
            ViewBag.IdOrdemPagamento = new SelectList(OrdemPagamentoBaas(), "Id", "Codigo", solicitacaoSaque.IdOrdemPagamento);
            ViewBag.IdSaidaCliente = new SelectList(SaidaClienteBaas(), "Id", "Descricao", solicitacaoSaque.IdSaidaCliente);
            ViewBag.IdStatusSolicitacaoSaque = new SelectList(db.StatusSolicitacaoSaque, "Id", "Nome", solicitacaoSaque.IdStatusSolicitacaoSaque);
            ViewBag.IdTipoOperacao = new SelectList(db.TipoOperacaoBancaria, "Id", "Nome", solicitacaoSaque.IdTipoOperacao);
            ViewBag.IdUsuarioEstorno = new SelectList(UsuarioAdministrativoBaas(), "Id", "Nome", solicitacaoSaque.IdUsuarioEstorno);

            if (TempData["Mensagem"] != null)
                ViewBag.Mensagem = TempData["Mensagem"];

            ViewBag.AguardandoAprovacao = false;

            if (solicitacaoSaque.IdStatusSolicitacaoSaque == 1)
            {
                var distributedCache = ServiceProvider.GetService<IDistributedCache>();
                if (distributedCache != null)
                {
                    var cached = await distributedCache.GetStringAsync($"AGUADANDO_APR_SAQUE:{id}");
                    ViewBag.AguardandoAprovacao = !string.IsNullOrWhiteSpace(cached);
                }
            }

            return View(solicitacaoSaque);
        }

        [HttpPost]
        [ExigePermissaoEscrita]
        [ValidateAntiForgeryToken]
        public virtual async Task<ActionResult> Edit([Bind(Include = "Id,IdCliente,IdSaidaCliente,IdStatusSolicitacaoSaque,IdContaBancaria,IdOrdemPagamento,IdTipoOperacao,IdUsuarioEstorno,IdContaBancariaEmpresaPagou,Codigo,ValorSolicitado,ValorTaxa,ValorTarifa,ValorLiquido,DataSolicitacao,JustificativaReprovacao,DataEstorno,JustificativaEstorno,DataProcessamento,DataPagamento,DataConclusao,Observacao")] PigPag.DBDomain.SolicitacaoSaque solicitacaoSaque)
        {
            if (ModelState.IsValid)
            {
                bool enviarEmailConclusao = false;
                if (solicitacaoSaque.IdStatusSolicitacaoSaque == Constant.StatusSolicitacaoSaque.Processado && !solicitacaoSaque.DataProcessamento.HasValue)
                    solicitacaoSaque.DataProcessamento = DateTime.Now;
                if (solicitacaoSaque.IdStatusSolicitacaoSaque == Constant.StatusSolicitacaoSaque.Confirmado && !solicitacaoSaque.DataPagamento.HasValue)
                {
                    if (!solicitacaoSaque.IdContaBancariaEmpresaPagou.HasValue)
                    {
                        TempData["Mensagem"] = "Informe a conta bancária da empresa de onde saiu o valor.";
                        return View(solicitacaoSaque);
                    }
                    solicitacaoSaque.DataPagamento = DateTime.Now;
                    solicitacaoSaque.DataConclusao = DateTime.Now;
                    enviarEmailConclusao = true;
                }
                db.Entry(solicitacaoSaque).State = EntityState.Modified;
                await db.SaveChangesAsync();

                if (enviarEmailConclusao)
                    if (!Request.Url.OriginalString.Contains("localhost"))
                    {
                        try
                        {
                            new Thread(async () =>
                            {
                                Thread.CurrentThread.IsBackground = true;
                                //Enviar e-mail
                                var cliente = await new Domain.Manager.Cliente(solicitacaoSaque.IdCliente).SelectClienteId(solicitacaoSaque.IdCliente);
                                db = new MultiPayContext();
                                var body = Resource.EmailSolicitacaoCompleto.Replace("[NOME_PESSOA]", cliente.NomeCliente)
                                                                                             .Replace("[CODIGO]", solicitacaoSaque.Codigo)
                                                                                             .Replace("[VALOR]", solicitacaoSaque.ValorSolicitado.ToString(Constant.Mascaras.MascaraMoedaBRL))
                                                                                             .Replace("[VALOR_TAXA]", solicitacaoSaque.ValorTaxa.ToString(Constant.Mascaras.MascaraMoedaBRL))
                                                                                             .Replace("[VALOR_TARIFA]", solicitacaoSaque.ValorTarifa.ToString(Constant.Mascaras.MascaraMoedaBRL))
                                                                                             .Replace("[VALOR_LIQUIDO]", solicitacaoSaque.ValorLiquido.ToString(Constant.Mascaras.MascaraMoedaBRL));
                                new PigPag.Email.Email().Enviar("PigPag - Solicitação de saque para própria conta concluído", new Email.Email.Destinatario()
                                {
                                    email = new System.Net.Mail.MailAddress(db.ContatoPessoa.First(x => x.IdPessoa == cliente.IdPessoa && x.Email != null).Email, cliente.NomeCliente),
                                    tipo = Email.Email.TipoDestinatario.Normal
                                }, body);
                            }).Start();
                        }
                        catch { }
                    }

                return RedirectToAction("Index");
            }
            ViewBag.IdCliente = new SelectList(ClientesBaas(), "Id", "Codigo", solicitacaoSaque.IdCliente);
            ViewBag.IdContaBancaria = new SelectList(ContaBancariaBaas(), "Id", "Agencia", solicitacaoSaque.IdContaBancaria);
            ViewBag.IdContaBancariaEmpresaPagou = new SelectList(db.ContaBancariaEmpresa, "Id", "Agencia", solicitacaoSaque.IdContaBancariaEmpresaPagou);
            ViewBag.IdOrdemPagamento = new SelectList(OrdemPagamentoBaas(), "Id", "Codigo", solicitacaoSaque.IdOrdemPagamento);
            ViewBag.IdSaidaCliente = new SelectList(SaidaClienteBaas(), "Id", "Descricao", solicitacaoSaque.IdSaidaCliente);
            ViewBag.IdStatusSolicitacaoSaque = new SelectList(db.StatusSolicitacaoSaque, "Id", "Nome", solicitacaoSaque.IdStatusSolicitacaoSaque);
            ViewBag.IdTipoOperacao = new SelectList(db.TipoOperacaoBancaria, "Id", "Nome", solicitacaoSaque.IdTipoOperacao);
            ViewBag.IdUsuarioEstorno = new SelectList(UsuarioAdministrativoBaas(), "Id", "Nome", solicitacaoSaque.IdUsuarioEstorno);
            return View(solicitacaoSaque);
        }

        public virtual async Task<ActionResult> LogSolicitacaoSaque(int id)
        {
            try
            {
                var logs = await LogSolicitacaoSaqueService.Singleton.ObterLogsPorSolicitacaoSaque(id);

                var cliente = (from s in db.SolicitacaoSaque
                               join cl in db.DadosBasicosPessoa on s.IdCliente equals cl.IdCliente
                               where s.Id == id
                               select new
                               {
                                   seCliente = !string.IsNullOrEmpty(cl.RazaoSocial)
                               })
                 .FirstOrDefault();

                var dataBase = logs.Select(item => new
                {
                    Id = item.IdSolicitacaoSaque,
                    Cliente = cliente?.seCliente == true,
                    Log = item.Texto,
                    Data = item.DataCriacaoUtc.ConvertUtcTimeToBrazilTimeZone()
                });

                var json = Json(dataBase, JsonRequestBehavior.AllowGet);
                json.MaxJsonLength = Int32.MaxValue;
                return json;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        [ExigePermissaoEscrita]
        public async Task<ActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            PigPag.DBDomain.SolicitacaoSaque solicitacaoSaque = await db.SolicitacaoSaque.FindAsync(id);
            if (solicitacaoSaque == null)
            {
                return HttpNotFound();
            }
            return View(solicitacaoSaque);
        }

        [HttpPost, ActionName("Delete")]
        [ExigePermissaoEscrita]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> DeleteConfirmed(int id)
        {
            PigPag.DBDomain.SolicitacaoSaque solicitacaoSaque = await db.SolicitacaoSaque.FindAsync(id);
            db.SolicitacaoSaque.Remove(solicitacaoSaque);
            await db.SaveChangesAsync();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }

        [HttpPost]
        [ExigePermissaoEscrita]
        [ValidateAntiForgeryToken]
        public ActionResult Processado(PigPag.DBDomain.SolicitacaoSaque model)
        {
            if (!model.IdTipoOperacao.HasValue)
            {
                TempData["MensagemErro"] = "Tipo de Operação obrigatório.";
                return RedirectToRoute("withdrawal_details", model.Id);
            }
            var solicitacao = db.SolicitacaoSaque.FirstOrDefault(x => x.Id == model.Id);
            var usuarioAdministrativo = new UsuarioAdministrativoModel().SelectUsuarioAdministrativoLogado();
            using (TransactionScope transaction = new TransactionScope())
            {
                solicitacao.IdStatusSolicitacaoSaque = Constant.StatusSolicitacaoSaque.Processado;
                solicitacao.DataProcessamento = DateTime.Now;
                solicitacao.DataPagamento = DateTime.Now;
                solicitacao.IdTipoOperacao = model.IdTipoOperacao;
                solicitacao.IdContaBancariaEmpresaPagou = model.IdContaBancariaEmpresaPagou;

                db.Entry(solicitacao).State = EntityState.Modified;
                db.SaveChanges();
                transaction.Complete();
            }
            TempData["Mensagem"] = "Solicitação marcada como processada";
            return RedirectToAction("edit", new { id = solicitacao.Id.ToString() });
        }

        [HttpPost]
        [ExigePermissaoEscrita]
        [ValidateAntiForgeryToken]
        public virtual async Task<ActionResult> Approve(PigPag.DBDomain.SolicitacaoSaque model)
        {
            var solicitacaoSaque = await db.SolicitacaoSaque.FirstOrDefaultAsync(x => x.Id == model.Id);
            var usuarioAdministrativo = new UsuarioAdministrativoModel().SelectUsuarioAdministrativoLogado();

            solicitacaoSaque.IdStatusSolicitacaoSaque = Constant.StatusSolicitacaoSaque.Confirmado;
            if (!solicitacaoSaque.DataProcessamento.HasValue)
                solicitacaoSaque.DataProcessamento = DateTime.Now;
            solicitacaoSaque.DataPagamento = DateTime.Now;
            solicitacaoSaque.DataConclusao = DateTime.Now;
            db.Entry(solicitacaoSaque).State = System.Data.Entity.EntityState.Modified;
            db.SaveChanges();

            TempData["Mensagem"] = await EnviarWebhookSolicitacaoSaque(solicitacaoSaque.Codigo); ;
            return RedirectToAction("edit", new { id = solicitacaoSaque.Id.ToString() });
        }

        [HttpPost]
        [ExigePermissaoEscrita]
        [ValidateAntiForgeryToken]
        public ActionResult Reject(PigPag.DBDomain.SolicitacaoSaque model)
        {
            if (string.IsNullOrEmpty(model.JustificativaReprovacao))
            {
                TempData["MensagemErro"] = "Justificativa obrigatória.";
                return RedirectToRoute("withdrawal_details", model.Id);
            }

            var solicitacao = db.SolicitacaoSaque.Include("SaidaCliente").FirstOrDefault(x => x.Id == model.Id);
            var saida = solicitacao.SaidaCliente;
            var usuarioAdministrativo = new UsuarioAdministrativoModel().SelectUsuarioAdministrativoLogado();

            solicitacao.IdStatusSolicitacaoSaque = Constant.StatusSolicitacaoSaque.Recusado;
            solicitacao.DataConclusao = DateTime.Now;
            solicitacao.JustificativaReprovacao = model.JustificativaReprovacao;

            new PigPag.Domain.Manager.EntradaCliente().InsertEntradaClienteExtratoContabil(new Domain.Manager.EntradaCliente.InsertEntradaClienteExtratoContabilRequestModel()
            {
                DataDesloqueio = DateTime.Now,
                Descricao = "ESTORNO SAQUE " + solicitacao.Codigo + " (MOTIVO ADM)",
                IdCliente = solicitacao.IdCliente,
                IdMoeda = Constant.Moeda.IdMoedaBRL,
                Valor = solicitacao.ValorSolicitado,
                ValorTarifa = 0.00M,
                ValorTaxa = 0.00M
            });

            db.Entry(solicitacao).State = System.Data.Entity.EntityState.Modified;
            db.SaveChanges();

            TempData["Mensagem"] = "Saque estornado com sucesso";
            return RedirectToAction("Edit", new { id = model.Id });
        }

        [HttpPost]
        [ExigePermissaoEscrita]
        [ValidateAntiForgeryToken]
        public virtual async Task<ActionResult> Return(PigPag.DBDomain.SolicitacaoSaque model)
        {
            if (string.IsNullOrEmpty(model.JustificativaEstorno))
            {
                TempData["MensagemErro"] = "Justificativa obrigatória.";
                return RedirectToRoute("withdrawal_details", model.Id);
            }

            var usuarioAdmin = new UsuarioAdministrativoModel().SelectUsuarioAdministrativoLogado();
            var solicitacao = db.SolicitacaoSaque.Include("SaidaCliente")
                .Where(item => usuarioAdmin.IdOperador == null || usuarioAdmin.IdOperador == item.Cliente.IdOperador)
                .FirstOrDefault(x => x.Id == model.Id);
            if (solicitacao == null)
            {
                TempData["MensagemErro"] = "Solicitação de saque não localizada.";
                return RedirectToRoute("withdrawal_details", model.Id);
            }
            if (solicitacao.DataPagamento.HasValue)
            {
                TempData["MensagemErro"] = "Não é possível estornar um saque já concluído.";
                return RedirectToRoute("withdrawal_details", model.Id);
            }
            if (!solicitacao.DataEstorno.HasValue)
                await new PigPag.Domain.Manager.SolicitacaoSaque().EstornarSaqueAsync(solicitacao.Id, model.JustificativaEstorno);

            new SolicitacaoSaqueModel().AdicionarLog(solicitacao.Id, string.Format("Estorno feito por: {0} - {1}", usuarioAdmin.Login, usuarioAdmin.Nome));

            if (!string.IsNullOrEmpty(solicitacao.UrlAtualizacao))
                await EnviarWebhookSolicitacaoSaque(solicitacao.Codigo);

            TempData["Mensagem"] = "Saque estornado com sucesso";
            return RedirectToAction("Edit", new { id = model.Id });
        }

        [HttpPost]
        [ExigePermissaoEscrita]
        [ValidateAntiForgeryToken]
        public virtual async Task<ActionResult> PagarViaPIX(PigPag.DBDomain.SolicitacaoSaque model)
        {
            var saque = db.SolicitacaoSaque.Include("SaidaCliente").FirstOrDefault(x => x.Id == model.Id);
            var contaBancaria = db.ContaBancariaFavorecidoCliente.Include(c => c.FavorecidoCliente).Single(x => x.Id == saque.IdContaBancariaFavorecidoCliente);

            var usuarioAdmin = new PigPag.Domain.Manager.UsuarioAdministrativo().SelectUsuarioAdministrativoLogado();

            new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("Comando de pagamento manual acionado"));
            new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("Pagamento manual efetuado por: {0} - {1}", usuarioAdmin.Login, usuarioAdmin.Nome));
            try
            {
                #region Enviando para pagamento

                var pixPayment = new MultiPay.PIX.Services.Saques(false);
                var pixModel = new MultiPay.PIX.Model.SaqueParaChaveRequestModel()
                {
                    CodigoSolicitacaoSaque = saque.Codigo,
                    IdSolicitacaoSaque = saque.Id,
                    Valor = saque.ValorSolicitado,
                    ContaBancariaRemetente = null,
                    ContaBancariaFavorecido = new MultiPay.PIX.Model.SaqueParaChaveRequestModel.DadosContaBancariaFavorecido(contaBancaria.IdTipoChavePIX == Constant.TipoChavePIX.CPFouCNPJ ? MultiPay.PIX.Model.BaseModel.TipoChavePix.CPFCNPJ : contaBancaria.IdTipoChavePIX == Constant.TipoChavePIX.Celular ? MultiPay.PIX.Model.BaseModel.TipoChavePix.TELEFONE : contaBancaria.IdTipoChavePIX == Constant.TipoChavePIX.Email ? MultiPay.PIX.Model.BaseModel.TipoChavePix.EMAIL : MultiPay.PIX.Model.BaseModel.TipoChavePix.EVP, contaBancaria.ChavePIX, contaBancaria.FavorecidoCliente.CPFCNPJ, contaBancaria.FavorecidoCliente.Nome)
                };

                // Enviando para pagamento
                Thread thread = new Thread(async () => await pixPayment.PagarParaChave(ServiceProvider, pixModel));
                thread.IsBackground = true;
                thread.Start();

                #endregion Enviando para pagamento

                TempData["Mensagem"] = "Solicitação de pagamento enviada para o banco";
                return RedirectToAction("edit", new { id = saque.Id.ToString() });
            }
            catch (BS2Exception ex)
            {
                new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("ERRO: {0}", ex.ResponseMessage));
                throw ex;
            }
            catch (Exception e)
            {
                new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("ERRO: {0}", e.Message));
                throw e;
            }
        }

        [HttpPost]
        [ExigePermissaoEscrita]
        [ValidateAntiForgeryToken]
        public ActionResult PagarViaTED(PigPag.DBDomain.SolicitacaoSaque model)
        {
            //var saque = db.SolicitacaoSaque.Include("SaidaCliente").FirstOrDefault(x => x.Id == model.Id);
            //var contaBancaria = db.ContaBancaria.Single(x => x.Id == saque.IdContaBancaria);
            //var banco = db.Banco.Single(x => x.Id == contaBancaria.IdBanco);
            //var cliente = db.DadosBasicosPessoa.Single(x => x.IdCliente == saque.IdCliente);
            //var usuarioAdministrativo = new UsuarioAdministrativoModel().SelectUsuarioAdministrativoLogado();

            //try
            //{
            //    new SolicitacaoSaqueModel().AdicionarLog(saque.Id, "Início processamento pagamento via TED");
            //    db.Database.ExecuteSqlCommand(string.Format("UPDATE SolicitacaoSaque SET IdStatusSolicitacaoSaque=2, DataProcessamento=GETDATE() WHERE Id={0}", saque.Id));
            //    DateTime dataPagamento = DateTime.Now;
            //    if (dataPagamento.DayOfWeek == DayOfWeek.Saturday)
            //        dataPagamento.AddDays(2);
            //    else if (dataPagamento.DayOfWeek == DayOfWeek.Sunday)
            //        dataPagamento.AddDays(1);
            //    if (dataPagamento.Hour > 16 || dataPagamento.Hour < 9)
            //        dataPagamento.AddDays(1);

            //    var bs2 = new Plugin.BS2.Services.TED(BS2Environment.PIXProduction);
            //    new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("Banco: {0} ({1})", banco.Numero, banco.Nome));
            //    new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("Agência: {0}", contaBancaria.Agencia));
            //    new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("Conta: {0}-{1}", contaBancaria.Conta, contaBancaria.DigitoVerificadorConta));
            //    new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("Tipo: {0}", contaBancaria.TipoContaBancaria.Nome));
            //    new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("Valor líquido: {0}", saque.ValorLiquido.ToString(Constant.Mascaras.MascaraMoedaBRL)));
            //    new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("Data prevista pagamento: {0}", dataPagamento.ToString()));
            //    new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("Enviando confirmação da TED para o banco"));
            //    var pagamento = bs2.FazerPagamento(new Plugin.BS2.Model.TED.FazerPagamentoRequestModel()
            //    {
            //        favorecido = new Plugin.BS2.Model.TED.FazerPagamentoRequestModel.Favorecido()
            //        {
            //            contaDestino = new Plugin.BS2.Model.TED.FazerPagamentoRequestModel.ContaDestino()
            //            {
            //                banco = new Plugin.BS2.Model.TED.FazerPagamentoRequestModel.Banco()
            //                {
            //                    codigo = Convert.ToInt32(banco.Numero),
            //                    nome = banco.NomeReduzido
            //                },
            //                agencia = contaBancaria.Agencia.PadLeft(4, '0'),
            //                numero = string.Format("{0}{1}", contaBancaria.Conta, contaBancaria.DigitoVerificadorConta).PadLeft(13, '4'),
            //                tipoConta = contaBancaria.IdTipoContaBancaria == Constant.TipoContaBancaria.ContaPagamento ? "1-Pagamento" : contaBancaria.IdTipoContaBancaria == Constant.TipoContaBancaria.ContaCorrente ? "2-Corrente" : "3-Poupanca"
            //            },
            //            documento = Common.StringFormat.RetornarApenasNumeros(cliente.CPFCNPJ),
            //            nome = Common.StringFormat.RetornarStringMaximoCaracteres(Common.StringFormat.ObterStringSemAcentosECaracteresEspeciais(cliente.IdTipoPessoa == Constant.TipoPessoa.PessoaFisica ? cliente.Nome : cliente.RazaoSocial), 60)
            //        },
            //        efetuarEm = dataPagamento.ToUniversalTime().ToString(),
            //        mesmaTitularidade = false,
            //        valor = saque.ValorLiquido
            //    });
            //    if (pagamento.executada)
            //    {
            //        new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("ID Solicitação TED: " + pagamento.solicitacaoId));
            //        new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("Transferência enviada"));
            //        db.Database.ExecuteSqlCommand(string.Format("UPDATE SolicitacaoSaque SET IdTipoOperacao=2, IdContaBancariaEmpresaPagou=1, DataPagamento='" + dataPagamento.ToString("yyyyMMdd HH:mm") + "', DataConclusao=GETDATE(), IdSolicitacaoTED='" + pagamento.solicitacaoId + "' WHERE Id={0}", saque.Id));
            //        //try
            //        //{
            //        //    //Enviar e-mail
            //        //    var body = Resource.EmailSolicitacaoCompleto.Replace("[NOME_PESSOA]", !string.IsNullOrEmpty(cliente.NomeFantasia) ? cliente.NomeFantasia : cliente.Nome)
            //        //                                                                 .Replace("[CODIGO]", saque.Codigo)
            //        //                                                                 .Replace("[VALOR]", saque.ValorSolicitado.ToString(Constant.Mascaras.MascaraMoedaBRL))
            //        //                                                                 .Replace("[VALOR_TAXA]", saque.ValorTaxa.ToString(Constant.Mascaras.MascaraMoedaBRL))
            //        //                                                                 .Replace("[VALOR_TARIFA]", saque.ValorTarifa.ToString(Constant.Mascaras.MascaraMoedaBRL))
            //        //                                                                 .Replace("[VALOR_LIQUIDO]", saque.ValorLiquido.ToString(Constant.Mascaras.MascaraMoedaBRL));
            //        //    new PigPag.Email.Email().Enviar("PigPag - Solicitação de saque para própria conta concluído", new Email.Email.Destinatario()
            //        //    {
            //        //        email = new System.Net.Mail.MailAddress(db.ContatoPessoa.First(x => x.IdPessoa == cliente.IdPessoa && x.Email != null).Email, !string.IsNullOrEmpty(cliente.NomeFantasia) ? cliente.NomeFantasia : cliente.Nome),
            //        //        tipo = Email.Email.TipoDestinatario.Normal
            //        //    }, body);
            //        //}
            //        //catch { }
            //        TempData["Mensagem"] = "Solicitação agendada com sucesso";
            //        return RedirectToAction("edit", new { id = saque.Id.ToString() });
            //    }
            //    else
            //    {
            //        new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("TED não processada"));
            //        new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("Motivo: " + pagamento.mensagem));
            //        TempData["Mensagem"] = "TED não processada";
            //        return RedirectToAction("edit", new { id = saque.Id.ToString() });
            //    }
            //}
            //catch (BS2Exception ex)
            //{
            //    new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("ERRO: {0}", ex.ResponseMessage));
            //    TempData["Mensagem"] = new MensagemModel(MensagemModel.TipoMensagem.Erro, ex.ResponseMessage);
            //    return RedirectToAction("index", "mensagem");
            //}
            //catch (Exception e)
            //{
            //    new SolicitacaoSaqueModel().AdicionarLog(saque.Id, string.Format("ERRO: {0}", e.Message));
            //    TempData["Mensagem"] = new MensagemModel(MensagemModel.TipoMensagem.Erro, e.Message);
            //    return RedirectToAction("index", "mensagem");
            //}

            throw new NotImplementedException();
        }

        public virtual async Task<ActionResult> _PartialListaSolicitacaoPorIdContaBancariaFavorecido(int idContaFavorecido)
        {
            try
            {
                var usuarioAdmin = new PigPag.Domain.Manager.UsuarioAdministrativo().SelectUsuarioAdministrativoLogado();
                var saques = db.SolicitacaoSaque
                    .Include(f => f.FavorecidoCliente)
                    .Include(x => x.ContaBancariaFavorecidoCliente)
                    .Where(x => x.IdContaBancariaFavorecidoCliente == idContaFavorecido)
                    .Where(item => usuarioAdmin.IdOperador == null || usuarioAdmin.IdOperador == item.Cliente.IdOperador);
                return PartialView(await saques.ToListAsync());
            }
            catch (Exception ex)
            {
                TempData["Mensagem"] = new MensagemModel()
                {
                    InnerException = ex,
                    Mensagem = ex.Message,
                    Tipo = MensagemModel.TipoMensagem.Erro
                };
                return RedirectToRoute("partial_mensagem");
            }
        }

        public virtual async Task<string> VerificarPagamentoIdNoBanco(ListaCodigos lista, CancellationToken cancellationToken)
        {
            await sharedService.VerifyWithdrawalsAsync(new VerifyWithdrawalsRequest { EndToEndIds = lista.Codigos });

            return $"Saque{(lista.Codigos.Count > 1 ? "s" : "")} enviado{(lista.Codigos.Count > 1 ? "s" : "")} para consulta no banco com sucesso! Volte a pesquisar em instantes para verificar se houve{(lista.Codigos.Count > 1 ? "ram" : "")} atualizaç{(lista.Codigos.Count > 1 ? "ões" : "ão")}.";
        }

        private async Task SaqueRejeitado(int saqueId)
        {
            var saque = await db.SolicitacaoSaque.FindAsync(saqueId);
            if (!saque.DataEstorno.HasValue)
                await new PigPag.Domain.Manager.SolicitacaoSaque().EstornarSaqueAsync(saque.Id, "REJEITADO PELA INSTITUIÇÃO RECEBEDORA");
            if (!string.IsNullOrEmpty(saque.UrlAtualizacao))
            {
                Console.WriteLine("Enviando Webhook");
                var saqueReprocessado = await new PigPag.Domain.Manager.SolicitacaoSaque().SelectSolicitacaoSaqueDadosBasicosPorCodigo(saque.Codigo);
                var body = new
                {
                    withdrawCode = saqueReprocessado.Codigo,
                    customId = saqueReprocessado.CustomId,
                    transactionCode = saqueReprocessado.CodigoTransacao,
                    updateCode = "03",
                    updateMessage = "REJECTED TRANSACTION"
                };
                /*******************************************************************************************
                 * ATUALIZANDO O STATUS
                 ******************************************************************************************/
                // Enviando a confirmação de pagamento ao sistema do cliente
                var urlPost = new UrlPost(saque.UrlAtualizacao);
                var request = await urlPost.SendPostUrlAsync(body);
                await new PigPag.Domain.Manager.EnvioWebhookSolicitacaoSaque().Insert(saque.Id, saque.Codigo, "PIX REJEITADO", saque.UrlAtualizacao, request.RequestJson, request.HttpResponseCode, request.ResponseJson);
            }
        }

        private async Task SaqueEfetivado(int saqueId)
        {
            var saque = await db.SolicitacaoSaque.FindAsync(saqueId);
            if (!saque.DataConclusao.HasValue || (!saque.DataPagamento.HasValue && !saque.DataEstorno.HasValue))
                await new PigPag.Domain.Manager.SolicitacaoSaque().ConfirmarSolicitacaoSaque(saque.Id, (long?)null);
            if (!string.IsNullOrEmpty(saque.UrlConfirmacao))
            {
                var saqueReprocessado = await new PigPag.Domain.Manager.SolicitacaoSaque().SelectSolicitacaoSaqueDadosBasicosPorCodigo(saque.Codigo);
                var transacionData = new
                {
                    operationType = saqueReprocessado.TipoOperacaoBancaria,
                    transactionDate = saqueReprocessado.DataSolicitacao,
                    completedDate = saqueReprocessado.DataConclusao,
                    chargebackDate = saqueReprocessado.DataEstorno,
                    paymentDate = saqueReprocessado.DataPagamento
                };
                var recipientData = new
                {
                    recipientName = saqueReprocessado.NomeFavorecido,
                    recipientDocumentID = saqueReprocessado.CPFCNPJFavorecido,
                    recipientBankAgency = saqueReprocessado.AgenciaFavorecido,
                    recipientBankAccount = saqueReprocessado.ContaFavorecido,
                    recipientPIXKeyType = saqueReprocessado.TipoChavePIX,
                    recipientPIXKey = saqueReprocessado.ChavePIX
                };
                var status = "";
                if (saqueReprocessado.DataPagamento.HasValue && saqueReprocessado.DataPagamento.HasValue && saqueReprocessado.DataConclusao.HasValue && !saqueReprocessado.DataEstorno.HasValue)
                    status = "Completed";
                else if (saqueReprocessado.DataEstorno.HasValue)
                    status = "Chargeback";
                else if (saqueReprocessado.DataPagamento.HasValue && !saqueReprocessado.DataConclusao.HasValue)
                    status = "Paid and Waiting Confirmation";
                else
                    status = "Unknown";
                var body = new
                {
                    error = false,
                    returnCode = "00",
                    returnMessage = "Success",
                    withdrawCode = saqueReprocessado.Codigo,
                    customId = saqueReprocessado.CustomId,
                    authenticationCode = saqueReprocessado.CodigoAutenticacao,
                    transactionCode = saqueReprocessado.CodigoTransacao,
                    transaction = transacionData,
                    recipient = recipientData,
                    requestDate = saqueReprocessado.DataSolicitacao,
                    paymentDate = saqueReprocessado.DataPagamento,
                    chargebackDate = saqueReprocessado.DataEstorno,
                    completionDate = saqueReprocessado.DataConclusao,
                    status = status
                };
                if (saqueReprocessado.DataPagamento.HasValue && saqueReprocessado.DataConclusao.HasValue && !saqueReprocessado.DataEstorno.HasValue)
                {
                    Console.WriteLine("Webhook Enviado");
                    /*******************************************************************************************
                     * CONFIRMANDO O PAGAMENTO DO SAQUE
                     ******************************************************************************************/
                    // Enviando a confirmação de pagamento ao sistema do cliente
                    var urlPost = new UrlPost(saqueReprocessado.UrlConfirmacao);
                    var request = await urlPost.SendPostUrlAsync(body);
                    await new PigPag.Domain.Manager.EnvioWebhookSolicitacaoSaque().Insert(saqueReprocessado.Id, saqueReprocessado.Codigo, "ENVIO DE PIX", saqueReprocessado.UrlConfirmacao, request.RequestJson, request.HttpResponseCode, request.ResponseJson);
                }
            }
        }

        public virtual async Task<ActionResult> ImprimirComprovante(string id)
        {
            try
            {
                var usuarioAdmin = new PigPag.Domain.Manager.UsuarioAdministrativo().SelectUsuarioAdministrativoLogado();
                var saque = await db.SolicitacaoSaque
                    .Where(item => usuarioAdmin.IdOperador == null || usuarioAdmin.IdOperador == item.Cliente.IdOperador)
                    .Include(x => x.ContaBancariaFavorecidoCliente)
                    .Include(x => x.ContaBancaria)
                    .FirstOrDefaultAsync(x => x.Codigo == id);

                if (saque == null)
                {
                    TempData["Mensagem"] = new MensagemModel(MensagemModel.TipoMensagem.Erro, "Solicitação de saque inválida");
                    return RedirectToAction("index", "mensagem");
                }

                string bancoApiRecebedor = "";
                var recebedorSolicitacaoSaque = await db.RecebedorSolicitacaoSaque.FirstOrDefaultAsync(x => x.IdSolicitacaoSaque == saque.Id);

                if (recebedorSolicitacaoSaque != null)
                {
                    var bancoApi = BancoApiService.ConsultarPorIspb(recebedorSolicitacaoSaque.ISPB);
                    bancoApiRecebedor = bancoApi?.Nome;
                }
                var model = new DetalheModel()
                {
                    Codigo = saque.Codigo,
                    DataConclusao = saque.DataConclusao,
                    DataEstorno = saque.DataEstorno,
                    DataPagamento = saque.DataPagamento,
                    DataProcessamento = saque.DataProcessamento,
                    DataSolicitacao = saque.DataSolicitacao,
                    JustificativaEstorno = saque.JustificativaEstorno,
                    JustificativaReprovacao = saque.JustificativaReprovacao,
                    ValorLiquido = saque.ValorLiquido,
                    ValorTarifa = saque.ValorTarifa,
                    ValorSolicitado = saque.ValorSolicitado,
                    ValorTaxa = saque.ValorTaxa,
                    NomeFavorecido = saque.FavorecidoCliente?.Nome,
                    CPFCNPJFavorecido = saque.FavorecidoCliente?.CPFCNPJ,
                    Agencia = saque.ContaBancaria?.Agencia,
                    AgenciaFavorecido = saque.ContaBancariaFavorecidoCliente?.Agencia?.Trim(),
                    Banco = $"{saque.ContaBancaria?.Banco?.Numero?.Trim()} - {saque.ContaBancaria?.Banco?.Nome?.Trim()}",
                    ChavePIX = saque.ContaBancariaFavorecidoCliente?.ChavePIX,
                    CodigoAutenticacao = saque.CodigoAutenticacao,
                    CodigoMovimento = saque.CodigoMovimento,
                    CodigoTransacao = saque.CodigoTransacao,
                    TipoOperacaoBancaria = saque.TipoOperacaoBancaria.Nome,
                    TipoChavePIX = saque.ContaBancariaFavorecidoCliente?.TipoChavePIX?.Nome,
                    IdTipoOperacao = saque.IdTipoOperacao,
                    BancoFavorecido = $"{saque.ContaBancariaFavorecidoCliente?.Banco?.Numero?.Trim()} - {saque.ContaBancariaFavorecidoCliente?.Banco?.Nome?.Trim()}",
                    ContaFavorecido = $"{saque.ContaBancariaFavorecidoCliente?.Conta?.Trim()}-{saque.ContaBancariaFavorecidoCliente?.DigitoVerificadorConta?.Trim()}",
                    BancoRecebedor = bancoApiRecebedor ?? string.Empty,
                    AgenciaRecebedor = recebedorSolicitacaoSaque != null ? recebedorSolicitacaoSaque?.Agencia : string.Empty,
                    ContaRecebedor = recebedorSolicitacaoSaque != null ? recebedorSolicitacaoSaque?.Conta : string.Empty
                };

                if (recebedorSolicitacaoSaque == null)
                {
                    if (string.IsNullOrEmpty(model.BancoRecebedor) && !string.IsNullOrEmpty(model.BancoFavorecido))
                        model.BancoRecebedor = model.BancoFavorecido;

                    if (string.IsNullOrEmpty(model.AgenciaRecebedor) && !string.IsNullOrEmpty(model.AgenciaFavorecido))
                        model.AgenciaRecebedor = model.AgenciaFavorecido;

                    if (string.IsNullOrEmpty(model.ContaRecebedor) && !string.IsNullOrEmpty(model.ContaFavorecido))
                        model.ContaRecebedor = model.ContaFavorecido;
                }

                ViewBag.IdSolicitacaoSaque = saque.Id;
                string viewContent = ConvertViewToString("ImprimirComprovante", model);
                //return PartialView("ImprimirComprovante", model);
                return new FileContentResult(PigPag.Common.Pdf.MontaBytesPDF(viewContent), "application/pdf");
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public virtual Task<string> ReenviarWebhookConfirmacaoPagamento(ListaCodigos lista)
        {
            var usuarioLogado = new Domain.Manager.UsuarioAdministrativo().SelectUsuarioAdministrativoLogado();

            var semaphore = new SemaphoreSlim(20);

            _ = Task.Run(async () =>
            {
                var tasks = lista.Codigos.Select(async codigo =>
                {
                    await semaphore.WaitAsync();

                    try
                    {
                        var solicitacaoSaque = await new Domain.Manager.SolicitacaoSaque().SelectSolicitacaoSaqueDadosBasicosPorCodigo(codigo, usuarioLogado);

                        if (solicitacaoSaque != null && (solicitacaoSaque.DataPagamento.HasValue || solicitacaoSaque.DataEstorno.HasValue) && !string.IsNullOrEmpty(solicitacaoSaque.UrlConfirmacao))
                            _ = await EnviarWebhookSolicitacaoSaque(solicitacaoSaque, $"{usuarioLogado.Login} - {usuarioLogado.Nome}");
                    }
                    catch
                    {
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                });

                await Task.WhenAll(tasks);
            });

            return Task.FromResult("Reenvio iniciado com sucesso. Os webhooks estão sendo enviados em segundo plano.");
        }

        [HttpPost]
        public async Task<string> ReenviarWebhookSequencial(string codigo)
        {
            try
            {
                var usuarioLogado = new Domain.Manager.UsuarioAdministrativo().SelectUsuarioAdministrativoLogado();
                var solicitacaoSaque = await new Domain.Manager.SolicitacaoSaque().SelectSolicitacaoSaqueDadosBasicosPorCodigo(codigo, usuarioLogado);

                if (solicitacaoSaque != null && (solicitacaoSaque.DataPagamento.HasValue || solicitacaoSaque.DataEstorno.HasValue) && !string.IsNullOrEmpty(solicitacaoSaque.UrlConfirmacao))
                {
                    var result = await EnviarWebhookSolicitacaoSaque(solicitacaoSaque, $"{usuarioLogado.Login} - {usuarioLogado.Nome}");
                    return result;
                }

                return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.alert, false, true, "Webhook não enviado - condições não atendidas"));
            }
            catch (Exception ex)
            {
                return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.error, true, true, $"Erro ao enviar webhook: {ex.Message}"));
            }
        }

        public ActionResult ReenviarWebhookEmLote()
        {
            return View();
        }

        [HttpPost]
        public void ReenviarSaqueFilaAsync(int idSolicitacaoSaque)
        {
            var bus = ServiceProvider.GetRequiredService<IBusReenviarSolicitacaoSaques>();
            bus.Publicar(new MultiPay.Plugin.RabbitMqBase.Events.ReenviarSolicitacaoSaqueEvent(null, idSolicitacaoSaque));
        }

        public virtual async Task<string> ObterSaquesParaReenviarWebhookEmLote(string di, string df)
        {
            try
            {
                if (string.IsNullOrEmpty(di))
                    return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.alert, true, false, $"Informe a data de início"));
                if (string.IsNullOrEmpty(df))
                    return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.alert, true, false, $"Informe a data de fim"));

                DateTime? dtI = null, dtF = null;
                if (!string.IsNullOrEmpty(di))
                    dtI = Convert.ToDateTime(di);
                if (!string.IsNullOrEmpty(df))
                    dtF = Convert.ToDateTime(df);

                var saques = await db.SolicitacaoSaque.Select(
                    saque => new
                    {
                        saque.Codigo,
                        saque.Id,
                        saque.DataSolicitacao,
                        saque.DataEstorno,
                        saque.DataPagamento,
                        saque.ValorSolicitado
                    }).Where(x => x.DataSolicitacao >= dtI && x.DataSolicitacao <= dtF)
                    .ToListAsync();

                if (!saques.Any())
                    return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.alert, true, false, $"Nenhum saque encontrado na busca"));

                return JsonConvert.SerializeObject(saques);
            }
            catch (Exception ex)
            {
                return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.error, true, false, $"ERRO: {ex.Message}"));
            }
        }

        [HttpGet]
        public string ObterEnd2EndSaque(int id)
        {
            return (db.SolicitacaoSaque.AsNoTracking().FirstOrDefault(a => a.Id == id)?.CodigoDevolucao) ?? (db.SolicitacaoSaque.AsNoTracking().FirstOrDefault(a => a.Id == id)?.CodigoAutenticacao);
        }
    }
}