@using PigPag.Resources;
<link href="~/plugins/datatables/PigShop/css/dataTables.bootstrap.min.css" rel="stylesheet" />
<link href="~/plugins/datatables/PigShop/buttons.bootstrap.min.css" rel="stylesheet" />
<script src="~/plugins/datatables/PigShop/js/jquery.dataTables.min.js"></script>
<script src="~/plugins/datatables/PigShop/js/dataTables.bootstrap4.min.js"></script>
<script src="~/plugins/datatables/PigShop/dataTables.buttons.min.js"></script>
<script src="~/plugins/datatables/PigShop/buttons.bootstrap.min.js"></script>
<script src="~/plugins/datatables/buttons.colVis.min.js"></script>
<script src="~/plugins/datatables/buttons.html5.min.js"></script>
<script src="~/plugins/datatables/buttons.print.min.js"></script>
<script src="~/plugins/datatables/jszip.min.js"></script>
<script src="~/plugins/datatables/pdfmake.min.js"></script>
<script src="~/plugins/datatables/vfs_fonts.js"></script>
<script src="~/plugins/datatables/PigShop/moment-with-locales-2.22.2.min.js"></script>

<style>
    .dt-buttons {
        float: right;
    }
</style>

<section class="content-header">
    <h1>Solicitações de Saque</h1>
</section>

<section class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="box box-success">
                <div class="box-header with-border">
                    <h3 class="box-title">@Resource.Filtros</h3>
                    <div class="box-tools pull-right">
                        <button type="button" class="btn btn-box-tool only-read" data-widget="collapse">
                            <i class="fa fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="box-body" style="display: block;">
                    <div class="row">
                        <div class="col-lg-3">
                            <div class="form-group">
                                Código do Saque
                                <input type="text" id="codigo" class="text_filtro only-read" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Número Fatura (CustomId)
                                <input type="text" id="customId" class="text_filtro only-read" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Data (Início)
                                <input id="dataInicio" name="dataInicio" value="@DateTime.Now.ToString("dd/MM/yyyy 00:00")" type="text" class="form-control only-read" data-inputmask="'alias': 'dd/mm/yyyy HH:mm'" data-mask="" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Data (Fim)
                                <input id="dataFim" name="dataFim" value="@DateTime.Now.ToString("dd/MM/yyyy 23:59")" type="text" class="form-control only-read" data-inputmask="'alias': 'dd/mm/yyyy HH:mm'" data-mask="" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                EndToEnd
                                <input type="text" id="endToEnd" class="text_filtro only-read" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Nome do Cliente
                                <input type="text" id="nomeCorrentista" class="text_filtro only-read" />
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-12">
                            <div class="form-group">
                                Banco
                                <select name="idBanco" id="idBanco" class=" only-read">
                                    @* Alterar para buscar os bancos ativos no SQL *@
                                    <option value="0">@PigPag.Resources.PigPag.Common.DropDownListResource.Todos</option>
                                    @*<option value="137">@PigPag.Resources.PigPag.Common.DropDownListResource.Bancos_Genial</option>
                                    <option value="238">@PigPag.Resources.PigPag.Common.DropDownListResource.Bancos_Aarin</option>*@
                                    <option value="232">@PigPag.Resources.PigPag.Common.DropDownListResource.Bancos_Delbank</option>
                                    @*<option value="115">@PigPag.Resources.PigPag.Common.DropDownListResource.Bancos_BS2</option>*@
                                    @*<option value="229">@PigPag.Resources.PigPag.Common.DropDownListResource.Bancos_Sicoob</opti*@on>
                                    <option value="231">CELCOIN</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-12">
                            <div class="form-group">
                                Status
                                <select name="statusSolicitacao" id="statusSolicitacao" class=" only-read">
                                    <option value="0">@PigPag.Resources.PigPag.Common.DropDownListResource.Todos</option>
                                    <option value="5">@PigPag.Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_Cancelado</option>
                                    <option value="3">@PigPag.Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_Confirmado</option>
                                    <option value="1">@PigPag.Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_EmAnalise</option>
                                    <option value="6">@PigPag.Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_Estornado</option>
                                    <option value="7">@PigPag.Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_PagamentoIniciado</option>
                                    <option value="2">@PigPag.Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_Processado</option>
                                    <option value="8">@PigPag.Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_ProcessoInterrompido</option>
                                    <option value="4">@PigPag.Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_Recusado</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Nome do Favorecido
                                <input type="text" id="nomeFavorecido" class="text_filtro only-read" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                CPF/CNPJ do Favorecido
                                <input type="text" id="cpfCnpjFavorecido" class="text_filtro only-read" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Origem de Transação
                                <select id="origemTransacao" name="origemTransacao" class="form-control only-read">
                                    <option value="0">@PigPag.Resources.PigPag.Common.DropDownListResource.Todos</option>
                                    <option value="API">API</option>
                                    <option value="PAINEL">PAINEL</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Itens por Página
                                <select id="itensPorPagina" name="itensPorPagina" class="form-control only-read">
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                    <option value="500">500</option>
                                    <option value="1000">1.000</option>
                                    <option value="5000">5.000</option>
                                    <option value="99999999">Total</option>
                                </select>

                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-4">
                            <button id="btnFiltrar" name="btnFiltrar" type="button" class="btn btn-primary only-read" onclick="PesquisarSaque(0);"><i class="fa fa-search"></i> Filtrar</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="box box-info">
                <div class="box-body">
                    @if (ViewBag.Mensagem != null)
                    {
                        <script>
                            $(function () {
                                MensagemOK('@ViewBag.Mensagem');
                            });
                        </script>
                    }

                    <div class="row">
                        <div class="col-lg-12">
                            <b><span id="reproc"></span></b>
                        </div>
                    </div>
                    <div class="row">

                        <div class="col-lg-12">
                            @Html.Partial("Table", new DataTablesModel
                            {
                           Info = true,
                           LengthMenu = "[10, 25, 50, 100, 500, 1000, 5000, -1], [10, 25, 50, 100, 500, 1000, 5000, 'Todos']",
                           Name = "listar-solicitacoes-saque",
                           FunctionGetUrlAjax = "getUrl",
                           Paging = false,
                           Ordering = false,
                           Processing = false,
                           ServerSide = false,
                           Search = false,
                           RefreshButton = false,
                           PrimaryKeyColumn = "Id",
                           Length = 25,
                           RowCallback = "rowColor",
                           Dom = "<'row'<'col-md-12't>>" +
                                "<'row margin-t-5'" +
                                "<'col-lg-10 col-xs-12'<'float-lg-left'p>>" +
                                "<'col-lg-2 col-xs-12'<'float-lg-right text-center'i>>" +
                                ">",
                           CustomButtons = new List<CustomButton>()
                           {
                               new CustomButton
                               {
                                   Name = "estornar-saque",
                                   Text = "<i class=\"fa fa-reply\" style=\"padding-left: 5px\"></i> Estornar Selecionados",
                                   TextAction = "EstornarSaquesSelecionados.call(this);",
                                   },
                               new CustomButton
                               {
                                   Name = "consultar-bacen",
                                   Text = "<i class=\"fa fa-eye\" style=\"padding-left: 5px\"></i> Consultar Selecionados",
                                   TextAction = "ConsultaBacen.call(this);",
                               },
                               new CustomButton
                               {
                                   Name = "reenviar-webhook",
                                   Text = "<i class=\"fa fa-send\" style=\"padding-left: 5px\"></i> Reenviar Webhook",
                                   TextAction = "ReenviarWebhook.call(this);",
                               }
                           },
                           ColumnCollection = new List<ColumnProperty>
                            {
                                new ColumnProperty("Codigo")
                                {
                                    Title = "",
                                    IsMasterCheckBox = true,
                                    Render = new RenderCheckBox("checkbox_saques"),
                                    ClassName =  "Estorno",
                                    AutoWidth = true,
                                    Ordering = false
                                },
                                new ColumnProperty("NomeRazaoSocialCliente")
                                {
                                    Title = "Cliente",
                                    Width = "250",
                                },
                                new ColumnProperty("NomeFavorecido")
                                {
                                    Title = "Favorecido",
                                    Width = "250",
                                },
                                new ColumnProperty("CPFCNPJFavorecido")
                                {
                                    Title = "CPF / CNPJ",
                                    Width = "95",
                                    Render = new RenderCustom("renderCpfCnpj")
                                },
                                new ColumnProperty("Codigo")
                                {
                                    Title = "Código",
                                    Width = "80",
                                },
                                new ColumnProperty("OrigemTransacao")
                                {
                                    Title = "Origem",
                                    Width = "60",
                                },
                                new ColumnProperty("CustomId")
                                {
                                    Title = "CustomId",
                                    Width = "100",
                                },
                                new ColumnProperty("ValorSolicitado")
                                {
                                    Title = "Valor",
                                    Render = new RenderCurrency("R$"),
                                    Width = "65",
                                    ClassName = PigPagColumnClassDefaults.TextRight,
                                },
                                new ColumnProperty("ValorTaxa")
                                {
                                    Title = "Taxa",
                                    Render = new RenderCurrency("R$"),
                                    Width = "65",
                                    ClassName = PigPagColumnClassDefaults.TextRight,
                                },
                                new ColumnProperty("ValorTarifa")
                                {
                                    Title = "Tarifa",
                                    Render = new RenderCurrency("R$"),
                                    Width = "65",
                                    ClassName = PigPagColumnClassDefaults.TextRight,
                                },
                                new ColumnProperty("ValorLiquido")
                                {
                                    Title = "Líquido",
                                    Render = new RenderCurrency("R$"),
                                    Width = "65",
                                    ClassName = PigPagColumnClassDefaults.TextRight,
                                },
                                new ColumnProperty("DataSolicitacao")
                                {
                                    Title = "Solicitado",
                                    Render = new RenderDate(),
                                    Width = "80",
                                    ClassName = PigPagColumnClassDefaults.CenterAll,
                                },
                                new ColumnProperty("DataProcessamento")
                                {
                                    Title = "Processado",
                                    Render = new RenderDate(),
                                    Width = "80",
                                    ClassName = PigPagColumnClassDefaults.CenterAll,
                                },
                                new ColumnProperty("DataPagamento")
                                {
                                    Title = "Pago",
                                    Render = new RenderDate(),
                                    Width = "80",
                                    ClassName = PigPagColumnClassDefaults.CenterAll,
                                },
                                new ColumnProperty("DataEstorno")
                                {
                                    Title = "Estornado",
                                    Render = new RenderDate(),
                                    Width = "80",
                                    ClassName = PigPagColumnClassDefaults.CenterAll,
                                },
                                new ColumnProperty("Status")
                                {
                                    Title = "Status",
                                    Width = "80",
                                    Render = new RenderCustom("renderColumnStatus"),
                                    ClassName = PigPagColumnClassDefaults.CenterAll,
                                },
                                new ColumnProperty("Id")
                                {
                                    Title = "",
                                    AutoWidth = true,
                                    ClassName = PigPagColumnClassDefaults.Button + PigPagColumnClassDefaults.CenterAll,
                                    Render = new RenderButtonView(new DataUrl("/solicitacaosaques/edit/"), hideText: true, newPage: true),
                                    Searchable = false,
                                    Ordering = false
                                },
                                new ColumnProperty("EndToEnd")
                                {
                                    Title = "",
                                    AutoWidth = true,
                                    ClassName = PigPagColumnClassDefaults.Button + PigPagColumnClassDefaults.CenterAll,
                                    Render = new RenderCustom("renderButtonComprovante"),
                                    Searchable = false,
                                    Ordering = false
                                }
                            }
                        })
                            <script>

                                function renderColumnCustomerEmail(data, type, row, meta) {
                                    var textRenderer = $.fn.dataTable.render.text().display;
                                    return textRenderer(row.CustomerFullName) + ' (' + textRenderer(row.CustomerEmail) + ')';
                                }

                                function renderButtonComprovante(data, type, row, meta) {

                                    if (data) {

                                        return "<a class=\"btn btn-default\" href=\"javascript:void(0)\" onclick=\"imprimirComprovante('" + data + "')\"" + "><i class=\"fa fa-print\"></i></a>";
                                    }
                                    else {

                                        return "<a class=\"btn btn-default\" href=\"javascript:void(0)\" disabled style=\"opacity: .5;\"><i class=\"fa fa-print\"></i></a>";
                                    }
                                }

                                function imprimirComprovante(e2e) {

                                    window.open('https://api.delbank.com.br/baas/api/v1/pix/' + e2e + '/proof', '_blank');
                                }

                                function renderColumnStatus(data, type, row, meta) {

                                    var color;
                                    if (row.Status == 'Estornado') {
                                        color = 'bg-red text-danger';
                                    }
                                    else if (row.Status == 'Confirmado') {
                                        color = 'bg-green text-success';
                                    }
                                    else if (data.Status == 'Pagamento Iniciado') {
                                        color = 'bg-primary text-primary';
                                    }
                                    else {
                                        color = 'bg-yellow text-warning';
                                    }
                                    return '<span class="badge ' + color + '">' + data + '</span >';
                                }

                                function rowColor(row, data, index) {
                                    if (data.Status == 'Estornado') {
                                        $(row).addClass('bg-red text-danger');
                                        $(row).prop('style', 'background-color: #dd4b3970 !important; color: #333 !important; font-weight: bold !important; border: 1px solid red; border-top: 1px solid red  !important;')
                                    }
                                    else if (data.Status == 'Confirmado') {
                                        $(row).addClass('bg-green text-success');
                                        $(row).prop('style', 'background-color: #00a65a70!important; color: #333 !important; font-weight: bold !important; border: 1px solid red; border-top: 1px solid red  !important;')
                                    }
                                    else if (data.Status == 'Pagamento Iniciado') {
                                        $(row).addClass('bg-primary text-primary');
                                        $(row).prop('style', 'background-color: #00c0ef70 !important; color: #333 !important; font-weight: bold !important; border: 1px solid red; border-top: 1px solid red  !important;')
                                    }
                                    else {
                                        $(row).addClass('bg-yellow text-warning');
                                        $(row).prop('style', 'background-color: #ede47a70 !important; color: #333 !important; font-weight: bold !important; border: 1px solid red; border-top: 1px solid red  !important;')
                                    }
                                }
                            </script>
                        </div>


                    </div>
                    <div class="row">
                        <div class="box-footer clearfix">
                            <b> Pagina: </b><span id="pagina">0</span>
                            <ul class="pagination pagination-sm no-margin pull-right">
                                <li><a readonly="javascript:$('#pagina').text()==0" href="javascript:paginate('-');">< Anterior</a></li>
                                <li><a readonly="javascript:$('#pagina').text()==0" href="javascript:paginate('+');">Proxima ></a></li>
                            </ul>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>

    $(document).ready(function () {

        var table = $('#listar-solicitacoes-saque').DataTable();

        table.on('xhr', () => {

            setTimeout(() => {

                $("td, th").css({
                    "vertical-align": "middle"
                });

                $("td.Estorno, th.Estorno").css({
                    "vertical-align": "middle",
                    "text-align": "center"
                });

                $("tr").css({
                    "vertical-align": "middle"
                });

                $("th.text-right").css({
                    "padding-right": "8px",
                    "padding-left": "30px"
                });

                $("th.text-center").css({
                    "padding-right": "19px",
                    "padding-left": "19px"
                });

                $("#mastercheckbox").css({
                    "margin-left": "11px",
                    "margin-right": "-11px"
                });

            }, 10);
        });
    });

    function getUrl(pg) {

        if (!pg)
            pg = 0;

        var url = '/solicitacaosaques/listarsolicitacaosaque/?pg=' + pg +
            '&codigo=' + $('#codigo').val() +
            '&customId=' + $('#customId').val() +
            '&endToEnd=' + $('#endToEnd').val() +
            '&nomeCorrentista=' + $('#nomeCorrentista').val() +
            '&cpfCnpjCorrentista=' + $('#cpfCnpjCorrentista').val() +
            '&nomeFavorecido=' + $('#nomeFavorecido').val() +
            '&cpfCnpjFavorecido=' + $('#cpfCnpjFavorecido').val() +
            '&di=' + $('#dataInicio').val() +
            '&df=' + $('#dataFim').val() +
            '&itensPorPagina=' + $('#itensPorPagina').val() +
            '&status=' + $('#statusSolicitacao').val() +
            '&idBanco=' + $('#idBanco').val() +
            '&origemTransacao=' + $('#origemTransacao').val();

        return url;
    }

    function PesquisarSaque(pg) {

        $('#pagina').text(pg);

        $("#dialogProcessando").modal('show');

        var table = $('#listar-solicitacoes-saque').DataTable();

        table.clear().draw();

        table.ajax.url(getUrl(pg)).load(function (result) {

            $("#dialogProcessando").modal('hide');
        });
    }

    function paginate(p) {

        if (p == '+') {
            $('#pagina').text(Number($('#pagina').text()) + 1);
            PesquisarSaque($('#pagina').text());
        }

        if (p == '-') {
            if (Number($('#pagina').text()) == 0) return;
            $('#pagina').text(Number($('#pagina').text()) - 1);
            PesquisarSaque($('#pagina').text());
        }
    }

    function ReenviarWebhook() {

        if (!selectedIds.length) {
            swal(
                'Oops...',
                'Nenhum registro selecionado. Selecione pelo menos um registro.',
                'error'
            )
            return;
        }

        swal({
            type: 'question',
            title: 'Atenção!!!',
            text: `${selectedIds.length} webhooks serão enviados. Deseja continuar?`,
            showLoaderOnConfirm: true,
            allowOutsideClick: true,
            allowEscapeKey: true,
            showCancelButton: true,
            confirmButtonText: 'Sim',
            cancelButtonText: 'Cancelar',
            preConfirm: () => {

                return new Promise((resolve, reject) => {

                    $.ajax({
                        url: '/SolicitacaoSaques/ReenviarWebhookConfirmacaoPagamento',
                        type: "POST",
                        data: { codigos: selectedIds },
                        success: function (response) {

                            resolve(response);
                        },
                        error: function (xhr, status, error) {

                            reject(error);
                        },
                        timeout: 600000
                    });
                });
            }
        }).then((result) => {

            selectedIds = [];

            $('input:checkbox').prop('checked', false);
            $('#reprocessarTudo').prop('checked', false);

            $("#dialogProcessando").modal('hide');

            swal(
                'Reenviar Webhooks',
                result,
                'success'
            );

        }).catch(swal.noop);
    }

    function EstornarSaquesSelecionados() {

        if (!selectedIds.length) {
            swal(
                'Oops...',
                'Nenhum registro selecionado. Selecione pelo menos um registro.',
                'error'
            )
            return;
        }

        swal({
            type: 'question',
            title: 'Atenção!!!',
            text: `${selectedIds.length} registros serão estornados. Deseja continuar?`,
            showLoaderOnConfirm: true,
            allowOutsideClick: true,
            allowEscapeKey: true,
            showCancelButton: true,
            confirmButtonText: 'Sim',
            cancelButtonText: 'Cancelar',
            preConfirm: () => {

                return new Promise((resolve, reject) => {

                    $.ajax({
                        url: '/SolicitacaoSaques/EstornarSolicitacaoSaqueMassa',
                        type: "POST",
                        data: { codigos: selectedIds },
                        success: function (response) {

                            resolve(response);
                        },
                        error: function (xhr, status, error) {

                            reject(error);
                        },
                        timeout: 600000
                    });
                });
            }
        }).then((result) => {

            selectedIds = [];

            $('input:checkbox').prop('checked', false);
            $('#reprocessarTudo').prop('checked', false);

            $("#dialogProcessando").modal('hide');

            swal(
                'Estornar Saques',
                result,
                'success'
            );

        }).catch(swal.noop);
    }

    function ConsultaBacen() {

        if (!selectedIds.length) {
            swal(
                'Oops...',
                'Nenhum registro selecionado. Selecione pelo menos um registro.',
                'error'
            )
            return;
        }

        swal({
            type: 'question',
            title: 'Atenção!!!',
            text: `${selectedIds.length} registros serão consultados. Deseja continuar?`,
            showLoaderOnConfirm: true,
            allowOutsideClick: true,
            allowEscapeKey: true,
            showCancelButton: true,
            confirmButtonText: 'Sim',
            cancelButtonText: 'Cancelar',
            preConfirm: () => {

                return new Promise((resolve, reject) => {

                    $.ajax({
                        url: 'SolicitacaoSaques/VerificarPagamentoIdnoBanco',
                        type: "POST",
                        data: { codigos: selectedIds },
                        success: function (response) {

                            resolve(response);
                        },
                        error: function (xhr, status, error) {

                            reject(error);
                        },
                        timeout: 600000
                    });
                });
            }
        }).then((result) => {

            selectedIds = [];

            $('input:checkbox').prop('checked', false);
            $('#reprocessarTudo').prop('checked', false);

            $("#dialogProcessando").modal('hide');

            swal(
                'Consulta Saques',
                result,
                'success'
            );

        }).catch(swal.noop);
    }

    function renderCpfCnpj(data, type, row, meta) {

        if (!data) return '';

        var numericData = data.replace(/\D/g, '');

        if (numericData.length === 11) {

            return numericData.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
        } else if (numericData.length === 14) {

            return numericData.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
        }

        return data;
    }

</script>